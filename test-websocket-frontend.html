<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .log { background: #f5f5f5; padding: 10px; margin: 10px 0; border-radius: 4px; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 10px 15px; margin: 5px; cursor: pointer; }
        #messages { max-height: 400px; overflow-y: auto; border: 1px solid #ddd; padding: 10px; }
    </style>
</head>
<body>
    <h1>WebSocket Notification Test</h1>
    
    <div>
        <button onclick="connectWebSocket()">Connect WebSocket</button>
        <button onclick="disconnectWebSocket()">Disconnect</button>
        <button onclick="sendTestMessage()">Send Test Message</button>
        <button onclick="clearMessages()">Clear Messages</button>
    </div>
    
    <div>
        <h3>Connection Status: <span id="status">Disconnected</span></h3>
        <h3>Messages:</h3>
        <div id="messages"></div>
    </div>

    <script>
        let ws = null;
        let userId = 1; // Test with admin user
        let token = null;

        function log(message, type = 'info') {
            const messages = document.getElementById('messages');
            const div = document.createElement('div');
            div.className = `log ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            messages.appendChild(div);
            messages.scrollTop = messages.scrollHeight;
        }

        function updateStatus(status) {
            document.getElementById('status').textContent = status;
        }

        async function getAuthToken() {
            try {
                // For testing, we'll generate the token manually
                // In real app, this would come from /notifications/websocket-token
                const testUserId = 1;
                const secretKey = 'secret_key';
                
                // Simple hash simulation (in real app this would be done server-side)
                const encoder = new TextEncoder();
                const data = encoder.encode(testUserId + secretKey);
                const hashBuffer = await crypto.subtle.digest('SHA-256', data);
                const hashArray = Array.from(new Uint8Array(hashBuffer));
                token = hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
                
                log(`Generated token for user ${testUserId}: ${token.substring(0, 20)}...`, 'success');
                return token;
            } catch (error) {
                log(`Failed to generate token: ${error.message}`, 'error');
                return null;
            }
        }

        async function connectWebSocket() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('WebSocket is already connected', 'warning');
                return;
            }

            try {
                log('Connecting to WebSocket server...', 'info');
                ws = new WebSocket('ws://localhost:8080');

                ws.onopen = async function() {
                    log('WebSocket connected successfully!', 'success');
                    updateStatus('Connected');
                    
                    // Get auth token and authenticate
                    if (!token) {
                        await getAuthToken();
                    }
                    
                    if (token) {
                        log('Sending authentication...', 'info');
                        ws.send(JSON.stringify({
                            type: 'auth',
                            user_id: userId,
                            token: token
                        }));
                    }
                };

                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`Received: ${JSON.stringify(data, null, 2)}`, 'success');
                        
                        if (data.type === 'auth_success') {
                            log('Authentication successful!', 'success');
                            updateStatus('Connected & Authenticated');
                        } else if (data.type === 'auth_error') {
                            log(`Authentication failed: ${data.message}`, 'error');
                        } else if (data.type === 'notification') {
                            log(`🔔 NOTIFICATION: ${data.data.title} - ${data.data.message}`, 'success');
                        }
                    } catch (error) {
                        log(`Received raw message: ${event.data}`, 'info');
                    }
                };

                ws.onclose = function() {
                    log('WebSocket connection closed', 'warning');
                    updateStatus('Disconnected');
                };

                ws.onerror = function(error) {
                    log(`WebSocket error: ${error}`, 'error');
                    updateStatus('Error');
                };

            } catch (error) {
                log(`Failed to connect: ${error.message}`, 'error');
                updateStatus('Error');
            }
        }

        function disconnectWebSocket() {
            if (ws) {
                ws.close();
                ws = null;
                log('WebSocket disconnected manually', 'info');
                updateStatus('Disconnected');
            }
        }

        function sendTestMessage() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                const message = {
                    type: 'ping'
                };
                ws.send(JSON.stringify(message));
                log(`Sent: ${JSON.stringify(message)}`, 'info');
            } else {
                log('WebSocket is not connected', 'error');
            }
        }

        function clearMessages() {
            document.getElementById('messages').innerHTML = '';
        }

        // Auto-connect on page load
        window.onload = function() {
            log('Page loaded. Click "Connect WebSocket" to test the connection.', 'info');
        };
    </script>
</body>
</html>
