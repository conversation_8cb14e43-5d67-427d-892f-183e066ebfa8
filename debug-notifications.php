<?php
/**
 * Debug Notifications Script
 * This script helps debug notification issues
 */

// Load configuration and dependencies
require_once __DIR__ . '/app/Config/config.php';
require_once __DIR__ . '/app/Helpers/functions.php';
require_once __DIR__ . '/app/Core/DB.php';
require_once __DIR__ . '/app/Notifications/NotificationManager.php';

echo "Notification System Debug\n";
echo "========================\n\n";

// Test 1: Check database connection
echo "1. Testing database connection...\n";
try {
    $users = DB::table('users')->limit(1)->get();
    echo "✓ Database connection successful\n";
} catch (Exception $e) {
    echo "✗ Database connection failed: " . $e->getMessage() . "\n";
    exit(1);
}

// Test 2: Check if notifications table exists
echo "\n2. Checking notifications table...\n";
try {
    $notifications = DB::table('notifications')->limit(1)->get();
    echo "✓ Notifications table exists\n";
} catch (Exception $e) {
    echo "✗ Notifications table missing or error: " . $e->getMessage() . "\n";
    echo "Please run the database migration to create the notifications table\n";
    exit(1);
}

// Test 3: Check recent notifications
echo "\n3. Checking recent notifications...\n";
try {
    $recentNotifications = DB::table('notifications')
        ->orderBy('created_at', 'DESC')
        ->limit(5)
        ->get();
    
    if (empty($recentNotifications)) {
        echo "No notifications found in database\n";
    } else {
        echo "Found " . count($recentNotifications) . " recent notifications:\n";
        foreach ($recentNotifications as $notif) {
            echo "  - ID: {$notif['id']}, Type: {$notif['type']}, User: {$notif['user_id']}, Created: {$notif['created_at']}\n";
        }
    }
} catch (Exception $e) {
    echo "✗ Error checking notifications: " . $e->getMessage() . "\n";
}

// Test 4: Check users and their roles
echo "\n4. Checking users and roles...\n";
try {
    $users = DB::table('users')->get();
    $adminCount = 0;
    $supportCount = 0;
    $clientCount = 0;
    
    foreach ($users as $user) {
        switch ($user['role']) {
            case 'admin':
                $adminCount++;
                break;
            case 'support':
                $supportCount++;
                break;
            case 'client':
                $clientCount++;
                break;
        }
    }
    
    echo "Users by role:\n";
    echo "  - Admins: $adminCount\n";
    echo "  - Support: $supportCount\n";
    echo "  - Clients: $clientCount\n";
    
    if ($adminCount === 0) {
        echo "⚠️  Warning: No admin users found. Notifications to admins will not work.\n";
    }
    
} catch (Exception $e) {
    echo "✗ Error checking users: " . $e->getMessage() . "\n";
}

// Test 5: Test NotificationManager
echo "\n5. Testing NotificationManager...\n";
try {
    $notificationManager = NotificationManager::getInstance();
    echo "✓ NotificationManager instance created\n";
    
    // Test WebSocket server status
    if ($notificationManager->isWebSocketServerRunning()) {
        echo "✓ WebSocket server is running\n";
    } else {
        echo "⚠️  WebSocket server is not running\n";
        echo "   Start it with: php app/websocket-server.php\n";
    }
    
} catch (Exception $e) {
    echo "✗ NotificationManager error: " . $e->getMessage() . "\n";
}

// Test 6: Create a test notification
echo "\n6. Creating test notification...\n";
try {
    $notificationManager = NotificationManager::getInstance();
    
    // Get first admin user
    $adminUser = DB::table('users')->where('role', 'admin')->first();
    if ($adminUser) {
        $testData = [
            'test' => true,
            'timestamp' => date('Y-m-d H:i:s')
        ];
        
        $notificationId = $notificationManager->createNotification(
            $adminUser['id'],
            'test_notification',
            'Test Notification',
            'This is a test notification created by the debug script',
            $testData
        );
        
        echo "✓ Test notification created with ID: $notificationId\n";
        echo "  Sent to admin user: {$adminUser['name']} (ID: {$adminUser['id']})\n";
    } else {
        echo "⚠️  No admin user found to send test notification\n";
    }
    
} catch (Exception $e) {
    echo "✗ Failed to create test notification: " . $e->getMessage() . "\n";
}

// Test 7: Check recent tickets
echo "\n7. Checking recent tickets...\n";
try {
    $recentTickets = DB::table('tickets as t')
        ->join('users as u', 't.client_id', '=', 'u.id')
        ->select(['t.id', 't.ticket_number', 't.subject', 't.status', 't.created_at', 'u.name as client_name'])
        ->orderBy('t.created_at', 'DESC')
        ->limit(3)
        ->get();
    
    if (empty($recentTickets)) {
        echo "No tickets found in database\n";
    } else {
        echo "Recent tickets:\n";
        foreach ($recentTickets as $ticket) {
            echo "  - #{$ticket['ticket_number']}: {$ticket['subject']} by {$ticket['client_name']} ({$ticket['status']})\n";
        }
    }
} catch (Exception $e) {
    echo "✗ Error checking tickets: " . $e->getMessage() . "\n";
}

// Test 8: Test direct WebSocket communication (if server is running)
echo "\n8. Testing direct WebSocket communication...\n";
try {
    $ch = curl_init('http://localhost:8081/status');
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 3);
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 2);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "⚠️  Cannot connect to WebSocket server: $error\n";
        echo "   Make sure to start: php app/websocket-server.php\n";
    } elseif ($httpCode === 200) {
        $status = json_decode($response, true);
        echo "✓ WebSocket server is responding\n";
        echo "  Connected users: " . count($status['connected_users']) . "\n";
        echo "  Total connections: " . $status['total_connections'] . "\n";
    } else {
        echo "⚠️  WebSocket server returned HTTP $httpCode\n";
    }
    
} catch (Exception $e) {
    echo "✗ WebSocket test failed: " . $e->getMessage() . "\n";
}

echo "\n========================\n";
echo "Debug completed!\n\n";

echo "Next steps:\n";
echo "1. If WebSocket server is not running, start it: php app/websocket-server.php\n";
echo "2. Create a new ticket and check if notifications appear in the database\n";
echo "3. Check browser console for WebSocket connection errors\n";
echo "4. Verify that admin/support users exist in the database\n";
