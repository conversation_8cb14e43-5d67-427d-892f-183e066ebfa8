<?php
/**
 * WebSocket Notification Test Script
 * This script tests the WebSocket notification system
 */

require_once __DIR__ . '/app/Config/config.php';
require_once __DIR__ . '/app/Helpers/functions.php';
require_once __DIR__ . '/app/Core/DB.php';
require_once __DIR__ . '/app/Notifications/NotificationManager.php';

echo "WebSocket Notification System Test\n";
echo "==================================\n\n";

// Test 1: Check if WebSocket server is running
echo "1. Checking WebSocket server status...\n";
$notificationManager = NotificationManager::getInstance();

if ($notificationManager->isWebSocketServerRunning()) {
    echo "✓ WebSocket server is running\n";
} else {
    echo "✗ WebSocket server is not running\n";
    echo "Please start the WebSocket server first: php app/websocket-server.php\n";
    exit(1);
}

// Test 2: Get server status
echo "\n2. Getting server status...\n";
$ch = curl_init('http://localhost:8081/status');
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_TIMEOUT, 5);
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

if ($httpCode === 200) {
    $status = json_decode($response, true);
    echo "✓ Server status retrieved successfully\n";
    echo "  Connected users: " . count($status['connected_users']) . "\n";
    echo "  Total connections: " . $status['total_connections'] . "\n";
    echo "  Timestamp: " . $status['timestamp'] . "\n";
} else {
    echo "✗ Failed to get server status (HTTP $httpCode)\n";
}

// Test 3: Send test notification to all admins
echo "\n3. Sending test notification to admins...\n";
try {
    $testData = [
        'test_id' => uniqid(),
        'message' => 'This is a test notification'
    ];
    
    $notificationManager->notifyRole('admin', 'test_notification', 'Test Notification', 'This is a test notification from the WebSocket system', $testData);
    echo "✓ Test notification sent to admins\n";
} catch (Exception $e) {
    echo "✗ Failed to send test notification: " . $e->getMessage() . "\n";
}

// Test 4: Send test notification to a specific user (if any users exist)
echo "\n4. Sending test notification to first user...\n";
try {
    $firstUser = DB::table('users')->where('status', 'active')->first();
    if ($firstUser) {
        $testData = [
            'test_id' => uniqid(),
            'user_id' => $firstUser['id']
        ];
        
        $notificationManager->createNotification(
            $firstUser['id'], 
            'test_notification', 
            'Personal Test Notification', 
            'This is a personal test notification for ' . $firstUser['name'], 
            $testData
        );
        echo "✓ Test notification sent to user: " . $firstUser['name'] . " (ID: " . $firstUser['id'] . ")\n";
    } else {
        echo "✗ No active users found in database\n";
    }
} catch (Exception $e) {
    echo "✗ Failed to send user notification: " . $e->getMessage() . "\n";
}

// Test 5: Test direct WebSocket communication
echo "\n5. Testing direct WebSocket communication...\n";
try {
    $testNotification = [
        'id' => uniqid(),
        'type' => 'test_notification',
        'title' => 'Direct WebSocket Test',
        'message' => 'This notification was sent directly to the WebSocket server',
        'data' => ['test' => true],
        'is_read' => false,
        'created_at' => date('Y-m-d H:i:s')
    ];
    
    $ch = curl_init('http://localhost:8081/notify');
    curl_setopt($ch, CURLOPT_POST, 1);
    curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode([
        'action' => 'broadcast',
        'notification' => $testNotification
    ]));
    curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 5);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($httpCode === 200) {
        $result = json_decode($response, true);
        if ($result && $result['success']) {
            echo "✓ Direct WebSocket communication successful\n";
        } else {
            echo "✗ WebSocket server returned error: " . $response . "\n";
        }
    } else {
        echo "✗ Direct WebSocket communication failed (HTTP $httpCode)\n";
    }
} catch (Exception $e) {
    echo "✗ Direct WebSocket test failed: " . $e->getMessage() . "\n";
}

echo "\n==================================\n";
echo "WebSocket test completed!\n";
echo "\nTo test the frontend:\n";
echo "1. Open your browser and login to the ticketing system\n";
echo "2. Open browser developer tools (F12) and check the Console tab\n";
echo "3. You should see WebSocket connection messages\n";
echo "4. Create a new ticket or update an existing one to see real-time notifications\n";
