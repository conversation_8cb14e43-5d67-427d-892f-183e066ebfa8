<?php


class DB
{
    private static ?PDO $connection = null;
    private string $table;
    private array $where = [];
    private array $orderBy = [];
    private array $joins = [];
    private array $groupBy = [];
    private array $having = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $bindings = [];
    private array $select = ['*'];

    // Method to get the connection
    public static function connection(): PDO
    {
        global $db_config ;
        if (self::$connection === null) {
            $host = $db_config['host'];
            $port = $db_config['port'];
            $name = $db_config['database'];
            $user = $db_config['username'];
            $pass = $db_config['password'];

            try {
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci;"
                    // PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8mb4 COLLATE utf8mb4_unicode_ci; SET time_zone = 'Africa/Tunis';"
                ];
    
                self::$connection = new PDO(
                    "mysql:host={$host};port={$port};dbname={$name};charset=utf8mb4",
                    $user,
                    $pass,
                    $options
                );
            } catch (PDOException $e) {
                logError($e);
                throw new Exception("Connection failed: " . $e->getMessage());
            }
        }
        return self::$connection;
    }
    

    // Query Builder Methods
    public static function table(string $table): self
    {
        $instance = new self();
        $instance->table = $table;
        return $instance;
    }

    public function select(array|string $columns): self
    {
        $this->select = is_array($columns) ? $columns : func_get_args();
        return $this;
    }

    public function where(string $column, string $operator, $value = null): self
    {
        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }
        
        $this->where[] = [$column, $operator, '?']; // Changed to use ? placeholder
        $this->bindings[] = $value;
        return $this;
    }

    public function whereIn(string $column, array $values): self
    {
        if(!$values || empty($values)) return $this;
        $placeholders = rtrim(str_repeat('?,', count($values)), ',');
        $this->where[] = [$column, 'IN', "($placeholders)"];
        $this->bindings = array_merge($this->bindings, $values);
        return $this;
    }


    public function whereBetween(string $column, $start, $end): self
    {
        $this->where[] = [$column, 'BETWEEN', '? AND ?'];
        $this->bindings[] = $start;
        $this->bindings[] = $end;
        return $this;
    }

    public function join(string $table, string $first, string $operator, string $second): self
    {
        $this->joins[] = ["INNER JOIN", $table, "$first $operator $second"];
        return $this;
    }

    public function leftJoin(string $table, string $first, string $operator, string $second): self
    {
        $this->joins[] = ["LEFT JOIN", $table, "$first $operator $second"];
        return $this;
    }

    public function groupBy(string|array $columns): self
    {
        $this->groupBy = is_array($columns) ? $columns : func_get_args();
        return $this;
    }

    public function having(string $column, string $operator, $value): self
    {
        $this->having[] = [$column, $operator, $value];
        $this->bindings[] = $value;
        return $this;
    }

    public function orderBy(string $column, string $direction = 'ASC'): self
    {
        $this->orderBy[] = [$column, strtoupper($direction)];
        return $this;
    }

    public function limit(int $limit): self
    {
        $this->limit = $limit;
        return $this;
    }

    public function offset(int $offset): self
    {
        $this->offset = $offset;
        return $this;
    }

    // Complex Operations
    public function insertBatch(array $records): bool
    {
        if (empty($records)) return false;
        
        $columns = implode(', ', array_keys($records[0]));
        $placeholders = '(' . implode(', ', array_fill(0, count($records[0]), '?')) . ')';
        $valuesList = implode(', ', array_fill(0, count($records), $placeholders));
        
        $sql = "INSERT INTO {$this->table} ($columns) VALUES $valuesList";
        $values = [];
        foreach ($records as $record) {
            $values = array_merge($values, array_values($record));
        }
        
        $stmt = self::connection()->prepare($sql);
        return $stmt->execute($values);
    }

    public function upsert(array $data, array $uniqueColumns): bool
    {
        $columns = array_keys($data);
        $placeholders = implode(', ', array_fill(0, count($data), '?'));
        $updates = array_map(fn($col) => "$col = VALUES($col)", array_diff($columns, $uniqueColumns));
        
        $sql = "INSERT INTO {$this->table} (" . implode(', ', $columns) . ") 
                VALUES ($placeholders) 
                ON DUPLICATE KEY UPDATE " . implode(', ', $updates);
        
        $stmt = self::connection()->prepare($sql);
        return $stmt->execute(array_values($data));
    }

    public function increment(string $column, int $amount = 1, array $conditions = []): bool
    {
        $sql = "UPDATE {$this->table} SET $column = $column + ? ";
        $bindings = [$amount];
        
        if (!empty($conditions)) {
            $whereConditions = [];
            foreach ($conditions as $key => $value) {
                $whereConditions[] = "$key = ?";
                $bindings[] = $value;
            }
            $sql .= "WHERE " . implode(' AND ', $whereConditions);
        }
        
        $stmt = self::connection()->prepare($sql);
        return $stmt->execute($bindings);
    }

    public function bulkDelete(array $ids): bool
    {
        $placeholders = str_repeat('?,', count($ids) - 1) . '?';
        $sql = "DELETE FROM {$this->table} WHERE id IN ($placeholders)";
        $stmt = self::connection()->prepare($sql);
        return $stmt->execute($ids);
    }

    // Transaction Methods
    public static function beginTransaction(): bool
    {
        return self::connection()->beginTransaction();
    }

    public static function commit(): bool
    {
        return self::connection()->commit();
    }

    public static function rollBack(): bool
    {
        return self::connection()->rollBack();
    }

    public static function transaction(callable $callback)
    {
        self::beginTransaction();
        try {
            $result = $callback();
            self::commit();
            return $result;
        } catch (Exception $e) {
            self::rollBack();
            throw $e;
        }
    }

    // Aggregates
    public function count(string $column = '*'): int
    {
        return (int) $this->aggregate("COUNT($column)");
    }

    public function max(string $column)
    {
        return $this->aggregate("MAX($column)");
    }

    public function min(string $column)
    {
        return $this->aggregate("MIN($column)");
    }

    public function avg(string $column)
    {
        return $this->aggregate("AVG($column)");
    }

    public function sum(string $column)
    {
        return $this->aggregate("SUM($column)");
    }

    private function aggregate(string $function)
    {
        $this->select = [$function];
        return $this->get()[0][$function] ?? null;
    }

    // Execute Query
    public function get(): array
    {
        try {
            $sql = $this->buildQuery();
            
            // // Debug information
            // if (defined('DEBUG') && DEBUG) {
            //     echo "SQL Query: " . $sql . "\n";
            //     echo "Bindings: " . print_r($this->bindings, true) . "\n";
            // }
            
            $stmt = self::connection()->prepare($sql);
            $stmt->execute($this->bindings);
            
            // Reset query builder state
            $result = $stmt->fetchAll(PDO::FETCH_ASSOC);
            $this->resetQueryBuilder();
            
            return $result;
        } catch (PDOException $e) {
            // Enhanced error handling
            $message = sprintf(
                "Database query error: %s\nQuery: %s\nBindings: %s",
                $e->getMessage(),
                $sql ?? 'No SQL query built',
                print_r($this->bindings, true)
            );
            
            if (defined('DEBUG') && DEBUG) {
                throw new Exception($message, 0, $e);
            }
            
            // Log error in production
            error_log($message);
            throw new Exception("Database error occurred. Check error logs for details.");
        }
    }

    public function first()
    {
        $this->limit(1);
        $result = $this->get();
        return $result[0] ?? null;
    }

    private function buildQuery(): string
    {
        $sql = "SELECT " . implode(', ', $this->select) . " FROM {$this->table}";

        // Add joins
        foreach ($this->joins as [$type, $table, $condition]) {
            $sql .= " $type $table ON $condition";
        }

        // Add where conditions
        if (!empty($this->where)) {
            $sql .= " WHERE ";
            $conditions = [];
            foreach ($this->where as [$column, $operator, $placeholder]) {
                $conditions[] = "$column $operator $placeholder";
            }
            $sql .= implode(' AND ', $conditions);
        }

        // Add group by
        if (!empty($this->groupBy)) {
            $sql .= " GROUP BY " . implode(', ', $this->groupBy);
        }

        // Add having
        if (!empty($this->having)) {
            $sql .= " HAVING ";
            $conditions = [];
            foreach ($this->having as [$column, $operator, $value]) {
                $conditions[] = "$column $operator ?";
            }
            $sql .= implode(' AND ', $conditions);
        }

        // Add order by
        if (!empty($this->orderBy)) {
            $sql .= " ORDER BY ";
            $orders = [];
            foreach ($this->orderBy as [$column, $direction]) {
                $orders[] = "$column $direction";
            }
            $sql .= implode(', ', $orders);
        }

        // Add limit and offset
        if ($this->limit !== null) {
            $sql .= " LIMIT " . $this->limit;
            if ($this->offset !== null) {
                $sql .= " OFFSET " . $this->offset;
            }
        }

        return $sql;
    }

    private function resetQueryBuilder(): void
    {
        $this->where = [];
        $this->orderBy = [];
        $this->joins = [];
        $this->groupBy = [];
        $this->having = [];
        $this->limit = null;
        $this->offset = null;
        $this->bindings = [];
        $this->select = ['*'];
    }

    // Debug method to see the generated SQL and bindings
    public function toSql(): array
    {
        return [
            'sql' => $this->buildQuery(),
            'bindings' => $this->bindings
        ];
    }

    // Enhanced CRUD Methods
    public static function create(string $table, array $data): bool|string
    {
        return (new self)->table($table)->insert($data);
    }

    public function insert(array $data): bool|string
    {
        $columns = implode(", ", array_keys($data));
        $placeholders = implode(", ", array_fill(0, count($data), '?'));
        $sql = "INSERT INTO {$this->table} ($columns) VALUES ($placeholders)";
        $stmt = self::connection()->prepare($sql);
        
        if ($stmt->execute(array_values($data))) {
            // Return the last inserted ID
            return self::connection()->lastInsertId();
        }
        
        // Return false if the insertion failed
        return false;
    }
    

    public static function read(string $table, array $columns = ['*']): array
    {
        return (new self)->table($table)->select($columns)->get();
    }

    public static function update(string $table, array $data, array $conditions): bool
    {
        $instance = new self;
        $instance->table = $table;
        
        $set = [];
        $values = [];
        foreach ($data as $key => $value) {
            $set[] = "$key = ?";
            $values[] = $value;
        }
        
        $where = [];
        foreach ($conditions as $key => $value) {
            $where[] = "$key = ?";
            $values[] = $value;
        }
        
        $sql = "UPDATE {$table} SET " . implode(', ', $set) . " WHERE " . implode(' AND ', $where);
        $stmt = self::connection()->prepare($sql);
        return $stmt->execute($values);
    }

    public static function delete(string $table, array $conditions): bool
    {
        $instance = new self;
        $instance->table = $table;
        
        $where = [];
        $values = [];
        foreach ($conditions as $key => $value) {
            $where[] = "$key = ?";
            $values[] = $value;
        }
        
        $sql = "DELETE FROM {$table} WHERE " . implode(' AND ', $where);
        $stmt = self::connection()->prepare($sql);
        return $stmt->execute($values);
    }
}