# WebSocket Notification System

This document explains the real-time notification system implemented for the ticketing application using WebSockets.

## Overview

The WebSocket notification system provides real-time notifications for:
- New ticket creation
- Ticket status changes
- New ticket replies
- Ticket assignments

## Architecture

### Components

1. **WebSocket Server** (`app/websocket-server.php`)
   - Handles WebSocket connections on port 8080
   - Provides HTTP API on port 8081 for sending notifications
   - Manages user authentication and connection tracking

2. **NotificationManager** (`app/Notifications/NotificationManager.php`)
   - Creates database notifications
   - Sends real-time notifications via WebSocket
   - Handles role-based and user-specific notifications

3. **Frontend Client** (`assets/js/notification-client.js`)
   - Connects to WebSocket server
   - Handles authentication
   - Displays toast notifications and updates UI

## Setup Instructions

### 1. Install Dependencies

Make sure you have the required PHP packages installed via Composer:

```bash
composer install
```

Required packages:
- `ratchet/pawl` - WebSocket client/server
- `react/socket` - Async socket server

### 2. Start the WebSocket Server

#### Option A: Using the batch file (Windows)
```bash
start-websocket-server.bat
```

#### Option B: Manual start
```bash
php app/websocket-server.php
```

The server will start on:
- WebSocket port: 8080
- HTTP API port: 8081

### 3. Test the System

Run the test script to verify everything is working:

```bash
php test-websocket.php
```

## How It Works

### Notification Flow

1. **Trigger Event**: User performs an action (creates ticket, updates status, etc.)
2. **Database Storage**: Notification is saved to the `notifications` table
3. **WebSocket Delivery**: Notification is sent to the WebSocket server via HTTP API
4. **Real-time Broadcast**: WebSocket server delivers notification to connected clients
5. **Frontend Display**: JavaScript client displays toast notification and updates UI

### Authentication

The WebSocket uses a simple token-based authentication:
- Token is generated using: `hash('sha256', $userId . 'secret_key')`
- Client authenticates by sending user ID and token
- Server verifies token before allowing notifications

### Notification Types

- `new_ticket` - New ticket created
- `ticket_status_change` - Ticket status updated
- `ticket_reply` - New reply added to ticket
- `ticket_assignment` - Ticket assigned to user

## API Endpoints

### WebSocket Server HTTP API (Port 8081)

#### POST /notify
Send notification to WebSocket clients

**Request Body:**
```json
{
    "action": "send_to_user|send_to_role|broadcast",
    "user_id": 123,           // For send_to_user
    "role": "admin",          // For send_to_role
    "notification": {
        "type": "new_ticket",
        "title": "New Ticket",
        "message": "A new ticket has been created",
        "data": {...}
    }
}
```

#### GET /status
Get server status and connected users

**Response:**
```json
{
    "success": true,
    "connected_users": [1, 2, 3],
    "total_connections": 3,
    "timestamp": "2024-01-01 12:00:00"
}
```

## Frontend Integration

### Automatic Connection

The notification client automatically connects when the page loads:

```javascript
// Automatically initialized in footer.php
window.notificationClient = new NotificationClient();
```

### Custom Event Handling

Listen for notifications in your JavaScript:

```javascript
document.addEventListener('newNotification', function(event) {
    const notification = event.detail;
    console.log('Received notification:', notification);
    
    // Handle specific notification types
    if (notification.type === 'new_ticket') {
        // Refresh ticket list, etc.
    }
});
```

## Troubleshooting

### Common Issues

1. **WebSocket server not starting**
   - Check if ports 8080 and 8081 are available
   - Ensure PHP has the required extensions
   - Check for firewall blocking the ports

2. **Notifications not appearing**
   - Verify WebSocket server is running
   - Check browser console for connection errors
   - Ensure user is authenticated

3. **Database notifications created but no real-time delivery**
   - Check if WebSocket server HTTP API is accessible
   - Verify cURL is working in PHP
   - Check server logs for errors

### Debug Mode

Enable debug logging by checking the WebSocket server console output. It will show:
- Connection events
- Authentication attempts
- Notification deliveries
- Errors and warnings

### Testing Connection

Use browser developer tools to test WebSocket connection:

```javascript
// Test WebSocket connection manually
const ws = new WebSocket('ws://localhost:8080');
ws.onopen = () => console.log('Connected');
ws.onmessage = (event) => console.log('Message:', event.data);
ws.onerror = (error) => console.log('Error:', error);
```

## Security Considerations

1. **Token Security**: The current implementation uses a simple hash. For production, consider using JWT tokens.

2. **CORS**: The WebSocket server accepts connections from any origin. Implement origin checking for production.

3. **Rate Limiting**: Consider implementing rate limiting to prevent notification spam.

4. **SSL/TLS**: For production, use WSS (WebSocket Secure) instead of WS.

## Performance Notes

- The WebSocket server can handle multiple concurrent connections
- Notifications are delivered in real-time with minimal latency
- Database notifications are stored regardless of WebSocket delivery status
- Failed WebSocket deliveries are logged but don't affect the application

## Future Enhancements

1. **Message Queuing**: Implement Redis or RabbitMQ for better scalability
2. **Clustering**: Support multiple WebSocket server instances
3. **Push Notifications**: Add browser push notification support
4. **Mobile Support**: Implement mobile app notifications
5. **Advanced Filtering**: Allow users to customize notification preferences
